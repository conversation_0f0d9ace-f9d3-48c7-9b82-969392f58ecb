<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevBlog - Responsive Blog Listing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        darkblue: {
                            900: '#0a192f',
                            800: '#172a45',
                            700: '#303f60',
                            600: '#4a6fa5',
                            500: '#64ffda',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .blog-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(10, 25, 47, 0.5);
        }
        .gradient-text {
            background: linear-gradient(90deg, #64ffda, #4a6fa5);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
    </style>
</head>
<body class="bg-darkblue-900 min-h-screen text-gray-300">
    <header class="py-6 px-4 sm:px-6 md:px-10 lg:px-16 bg-darkblue-800 shadow-lg">
        <div class="max-w-screen-xl mx-auto flex flex-wrap items-center justify-between gap-y-4">
            <h1 class="text-2xl md:text-3xl font-bold gradient-text">DevBlog</h1>
            <nav class="w-full sm:w-auto">
                <ul class="flex flex-wrap justify-center sm:justify-start gap-4 sm:space-x-6 text-sm">
                    <li><a href="#" class="hover:text-blue-300 transition">Home</a></li>
                    <li><a href="#" class="hover:text-blue-300 transition">Categories</a></li>
                    <li><a href="#" class="hover:text-blue-300 transition">About</a></li>
                    <li><a href="#" class="hover:text-blue-300 transition">Contact</a></li>
                </ul>
            </nav>
            <button class="w-full sm:w-auto bg-darkblue-500 text-darkblue-900 px-4 py-2 rounded-full font-semibold hover:bg-opacity-90 transition">
                Subscribe
            </button>
        </div>
    </header>

    <main class="max-w-screen-xl mx-auto py-12 px-4 sm:px-6 md:px-10 lg:px-16">
        <div class="flex flex-wrap items-center justify-between mb-8 gap-4">
            <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-white">Latest Articles</h2>
            <select class="bg-darkblue-800 border border-darkblue-700 text-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-darkblue-500">
                <option>Latest First</option>
                <option>Popular</option>
                <option>Oldest First</option>
            </select>
        </div>

        <div class="grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
            <!-- Blog Card 1 -->
            <div class="blog-card bg-darkblue-800 rounded-xl overflow-hidden border border-darkblue-700 transition duration-300 ease-in-out flex flex-col">
                <div class="h-48 sm:h-52 md:h-48 overflow-hidden">
                    <img src="https://source.unsplash.com/random/600x400/?technology" alt="Blog Image" class="w-full h-full object-cover hover:scale-105 transition duration-500">
                </div>
                <div class="p-6 flex flex-col flex-grow">
                    <div class="flex items-center mb-2">
                        <span class="bg-darkblue-700 text-darkblue-500 text-xs px-3 py-1 rounded-full">Tech</span>
                        <span class="ml-auto text-xs text-gray-400"><i class="far fa-clock mr-1"></i> 7 min read</span>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 hover:text-darkblue-500 transition">Next-Gen Tech Innovations</h3>
                    <p class="text-gray-400 mb-4">Explore emerging technologies transforming the world in 2025 and beyond.</p>
                    <div class="flex items-center mt-auto">
                        <div class="w-8 h-8 rounded-full overflow-hidden mr-3">
                            <img src="https://randomuser.me/api/portraits/men/11.jpg" alt="Author" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="text-sm font-medium text-white">John Miller</p>
                            <p class="text-xs text-gray-400">Jun 15, 2025 · 11:00 AM</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blog Card 2 -->
            <div class="blog-card bg-darkblue-800 rounded-xl overflow-hidden border border-darkblue-700 transition duration-300 ease-in-out flex flex-col">
                <div class="h-48 sm:h-52 md:h-48 overflow-hidden">
                    <img src="https://source.unsplash.com/random/600x400/?coding" alt="Blog Image" class="w-full h-full object-cover hover:scale-105 transition duration-500">
                </div>
                <div class="p-6 flex flex-col flex-grow">
                    <div class="flex items-center mb-2">
                        <span class="bg-darkblue-700 text-darkblue-500 text-xs px-3 py-1 rounded-full">Programming</span>
                        <span class="ml-auto text-xs text-gray-400"><i class="far fa-clock mr-1"></i> 5 min read</span>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 hover:text-darkblue-500 transition">Clean Code Practices</h3>
                    <p class="text-gray-400 mb-4">Learn essential tips to write clean, readable, and maintainable code.</p>
                    <div class="flex items-center mt-auto">
                        <div class="w-8 h-8 rounded-full overflow-hidden mr-3">
                            <img src="https://randomuser.me/api/portraits/women/24.jpg" alt="Author" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="text-sm font-medium text-white">Nina Patel</p>
                            <p class="text-xs text-gray-400">Jun 14, 2025 · 2:30 PM</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blog Card 3 -->
            <div class="blog-card bg-darkblue-800 rounded-xl overflow-hidden border border-darkblue-700 transition duration-300 ease-in-out flex flex-col">
                <div class="h-48 sm:h-52 md:h-48 overflow-hidden">
                    <img src="https://source.unsplash.com/random/600x400/?ux-design" alt="Blog Image" class="w-full h-full object-cover hover:scale-105 transition duration-500">
                </div>
                <div class="p-6 flex flex-col flex-grow">
                    <div class="flex items-center mb-2">
                        <span class="bg-darkblue-700 text-darkblue-500 text-xs px-3 py-1 rounded-full">UX Design</span>
                        <span class="ml-auto text-xs text-gray-400"><i class="far fa-clock mr-1"></i> 6 min read</span>
                    </div>
                    <h3 class="text-xl font-bold text-white mb-2 hover:text-darkblue-500 transition">Crafting Intuitive Interfaces</h3>
                    <p class="text-gray-400 mb-4">How thoughtful design choices can improve usability and user satisfaction.</p>
                    <div class="flex items-center mt-auto">
                        <div class="w-8 h-8 rounded-full overflow-hidden mr-3">
                            <img src="https://randomuser.me/api/portraits/men/46.jpg" alt="Author" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <p class="text-sm font-medium text-white">Leo Anders</p>
                            <p class="text-xs text-gray-400">Jun 13, 2025 · 9:10 AM</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-12 flex justify-center">
            <nav class="flex items-center space-x-2">
                <button class="px-4 py-2 rounded-full bg-darkblue-700 text-gray-300 hover:bg-darkblue-600 transition">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="w-10 h-10 rounded-full bg-darkblue-500 text-darkblue-900 font-bold">1</button>
                <button class="w-10 h-10 rounded-full bg-darkblue-700 text-gray-300 hover:bg-darkblue-600 transition">2</button>
                <button class="w-10 h-10 rounded-full bg-darkblue-700 text-gray-300 hover:bg-darkblue-600 transition">3</button>
                <span class="px-2 text-gray-400">...</span>
                <button class="w-10 h-10 rounded-full bg-darkblue-700 text-gray-300 hover:bg-darkblue-600 transition">8</button>
                <button class="px-4 py-2 rounded-full bg-darkblue-700 text-gray-300 hover:bg-darkblue-600 transition">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </nav>
        </div>
    </main>

    <footer class="bg-darkblue-800 py-12 px-4 sm:px-6 md:px-10 lg:px-16 mt-12">
        <div class="max-w-screen-xl mx-auto">
            <div class="grid gap-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
                <div>
                    <h3 class="text-xl font-bold text-white mb-4">DevBlog</h3>
                    <p class="text-gray-400">Sharing knowledge and insights about technology, design, and development.</p>
                    <div class="flex space-x-4 mt-4">
                        <a href="#" class="text-gray-400 hover:text-darkblue-500 transition"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-gray-400 hover:text-darkblue-500 transition"><i class="fab fa-github"></i></a>
                        <a href="#" class="text-gray-400 hover:text-darkblue-500 transition"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="text-gray-400 hover:text-darkblue-500 transition"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Categories</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Web Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Mobile Apps</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">UI/UX Design</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Artificial Intelligence</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-sm">
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">About Us</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Contact</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-darkblue-500 transition">Terms of Service</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Subscribe</h4>
                    <p class="text-gray-400 mb-4 text-sm">Get the latest articles delivered to your inbox.</p>
                    <div class="flex w-full max-w-md">
                        <input type="email" placeholder="Your email" class="flex-grow bg-darkblue-700 text-white px-4 py-2 rounded-l-full focus:outline-none focus:ring-2 focus:ring-darkblue-500">
                        <button class="bg-darkblue-500 text-darkblue-900 px-4 py-2 rounded-r-full font-semibold hover:bg-opacity-90 transition">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-darkblue-700 mt-8 pt-8 text-center text-gray-400 text-sm">
                <p>&copy; 2023 DevBlog. All rights reserved.</p>
            </div>
        </div>
    </footer>
</body>
</html>
