.elementor-motion-effects-element,.elementor-motion-effects-layer{transition-duration:1s;transition-property:transform,opacity;transition-timing-function:cubic-bezier(0,.33,.07,1.03)}.elementor-motion-effects-container{height:100%;left:0;overflow:hidden;position:absolute;top:0;transform-origin:var(--e-transform-origin-y) var(--e-transform-origin-x);width:100%}.elementor-motion-effects-layer{background-repeat:no-repeat;background-size:cover;left:0;position:absolute;top:0}.elementor-motion-effects-perspective{perspective:1200px}.elementor-motion-effects-element{transform-origin:var(--e-transform-origin-y) var(--e-transform-origin-x)}