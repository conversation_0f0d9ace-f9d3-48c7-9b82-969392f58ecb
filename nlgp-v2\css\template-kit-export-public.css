/* ../scss/public/template-kit-export-public.scss */
.template-kit-preview {
  background: #e9e9e9;
  color: rgba(0, 0, 0, 0.75);
  font: 1em/1.67 <PERSON>l, Sans-serif;
  margin: 0;
  padding: 0.5em;
}
@media (min-width: 800px) {
  .template-kit-preview {
    padding: 1em 2em;
  }
}
.template-kit-preview__title {
  font-size: 2em;
}
.template-kit-preview__grid {
  transition: all 0.5s ease-in-out;
  column-gap: 30px;
  column-fill: initial;
}
@media (min-width: 400px) {
  .template-kit-preview__grid {
    column-count: 2;
  }
}
@media (min-width: 800px) {
  .template-kit-preview__grid {
    column-count: 4;
  }
}
.template-kit-preview__template {
  display: inline-block;
  vertical-align: top;
  background: #fff;
  padding: 1em;
  margin: 0 0 1.5em;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 1px 1px 0px rgba(0, 0, 0, 0.18);
  border-radius: 3px;
  min-height: 100px;
}
.template-kit-preview__screenshot {
  padding: 0;
  margin: 0;
  line-height: 0;
}
.template-kit-preview__name {
  text-decoration: none;
  text-align: center;
  padding: 10px;
}
/*# sourceMappingURL=template-kit-export-public.css.map */
