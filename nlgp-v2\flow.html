<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Lead Lifecycle Visualization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/scrollreveal"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Universal Chat Widget -->
  <script src="universal-chat-widget.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --primary: #2563eb;
            --secondary: #3b82f6;
            --accent: #06b6d4;
            --dark: #1e293b;
            --light: #f8fafc;
            --glass: rgba(255, 255, 255, 0.15);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: var(--dark);
            overflow-x: hidden;
        }
        
        .glass-card {
            background: var(--glass);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        }
        
        .timeline-connector {
            position: relative;
        }
        
        .timeline-connector::after {
            content: '';
            position: absolute;
            width: 4px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
            top: 100%;
            bottom: -2rem;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(59, 130, 246, 0.1), 0 10px 10px -5px rgba(59, 130, 246, 0.04);
        }
        
        .radar {
            width: 120px;
            height: 120px;
            position: relative;
            background: conic-gradient(from 0deg, rgba(37, 99, 235, 0.1) 0%, rgba(6, 182, 212, 0.3) 30%, rgba(37, 99, 235, 0.1) 70%);
            border-radius: 50%;
            overflow: hidden;
        }
        
        .radar::before {
            content: '';
            position: absolute;
            width: 80%;
            height: 80%;
            background: var(--glass);
            border-radius: 50%;
            top: 10%;
            left: 10%;
        }
        
        .radar-scan {
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);
            animation: scan 4s linear infinite;
            transform-origin: left center;
        }
        
        @keyframes scan {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
        
        .signal-dot {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--accent);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.7);
            }
            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(6, 182, 212, 0);
            }
            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(6, 182, 212, 0);
            }
        }
        
        .phone-ring {
            animation: ring 1.5s ease-in-out infinite;
            transform-origin: 50% 0;
        }
        
        @keyframes ring {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(10deg);
            }
            75% {
                transform: rotate(-10deg);
            }
        }
        
        .message-bubble {
            position: relative;
            max-width: 220px;
            padding: 12px;
            border-radius: 18px;
            margin: 4px 0;
            animation: slideIn 0.3s ease-out forwards;
            opacity: 0;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(10px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .typing-indicator {
            display: flex;
            padding: 10px;
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
            }
            30% {
                transform: translateY(-5px);
            }
        }
        
        .countdown {
            font-family: monospace;
            font-size: 1.2rem;
            color: var(--accent);
        }
        
        .reactivating {
            animation: glow 2s infinite alternate;
        }
        
        @keyframes glow {
            0% {
                box-shadow: 0 0 5px -5px var(--accent);
            }
            100% {
                box-shadow: 0 0 20px 5px var(--accent);
            }
        }
        
        @media (max-width: 768px) {
            .timeline-connector::after {
                left: 1.5rem;
                transform: none;
            }
            
            .mobile-timeline-item {
                padding-left: 3rem !important;
            }
        }
    </style>
</head>
<body class="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">AI-Powered Lead Lifecycle</h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">Witness how our automation converts prospects to customers at scale</p>
        </div>
        
        <!-- Lead Lifecycle Timeline -->
        <div class="relative">
            <!-- Lead Generation -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="lead-generation">
                <div class="w-full md:w-1/2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="radar mb-6">
                                <div class="radar-scan"></div>
                                <div class="signal-dot" style="top: 30%; left: 60%;"></div>
                                <div class="signal-dot" style="top: 45%; left: 25%;"></div>
                                <div class="signal-dot" style="top: 70%; left: 70%;"></div>
                            </div>
                            <h3 class="text-2xl font-semibold text-center mb-3">Lead Generation</h3>
                            <p class="text-center text-gray-600 mb-6">AI finds your ideal customers across multiple data sources</p>
                            <div class="w-64 h-3 bg-gradient-to-r from-blue-100 via-blue-300 to-cyan-300 rounded-full mb-6"></div>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">1. AI-Powered Lead Discovery</h2>
                    <p class="text-lg text-gray-700 mb-6">Our system scans millions of data points to identify your perfect customers based on behavior patterns, intent signals, and ideal customer profiles.</p>
                    <div class="bg-white/50 p-4 rounded-xl mb-4">
                        <div class="text-sm font-mono text-gray-600 overflow-hidden">
                            <div class="typing-indicator">
                                <span class="typing-dot"></span>
                                <span class="typing-dot"></span>
                                <span class="typing-dot"></span>
                            </div>
                            <div class="pl-4">
                                >> Scanning LinkedIn, Crunchbase, Google<br>
                                >> Analyzing 1,243 matching companies<br>
                                >> Verified 379 decision-maker contacts<br>
                                >> Loading into automation sequence...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Cold Outreach -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="cold-outreach">
                <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="flex space-x-4 mb-6">
                                <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                    <i class="fas fa-envelope text-3xl text-blue-500"></i>
                                </div>
                                <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                    <i class="fas fa-comment-dots text-3xl text-green-500"></i>
                                </div>
                                <div class="bg-white/90 p-4 rounded-xl shadow-md">
                                    <i class="fab fa-linkedin text-3xl text-blue-600"></i>
                                </div>
                            </div>
                            <div class="space-y-2 w-full mb-6">
                                <div class="message-bubble bg-blue-500 text-white ml-auto rounded-tl-xl rounded-bl-xl rounded-br-xl" style="animation-delay: 0.2s;">
                                    Hi [First Name], noticed your recent [trigger event]...
                                </div>
                                <div class="message-bubble bg-gray-200 text-gray-800 rounded-tr-xl rounded-bl-xl rounded-br-xl" style="animation-delay: 0.4s;">
                                    Thanks for reaching out! Can you send more info?
                                </div>
                                <div class="message-bubble bg-blue-500 text-white ml-auto rounded-tl-xl rounded-bl-xl rounded-br-xl" style="animation-delay: 0.6s;">
                                    Absolutely! Here's our case study →
                                </div>
                            </div>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">2. Multi-Channel Outreach</h2>
                    <p class="text-lg text-gray-700 mb-6">Personalized messages across email, SMS, and social channels - with AI optimizing timing, messaging, and follow-ups based on engagement.</p>
                    <div class="space-y-2">
                        <div class="flex items-center bg-white/50 p-3 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-check text-green-500"></i>
                            </div>
                            <span>87% open rate compared to industry 27%</span>
                        </div>
                        <div class="flex items-center bg-white/50 p-3 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-bolt text-yellow-500"></i>
                            </div>
                            <span>Automatic response handling for 24/7 coverage</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Lead Conversion -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="lead-conversion">
                <div class="w-full md:w-1/2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="relative mb-6">
                                <div class="w-48 h-32 bg-white/90 rounded-xl shadow-md p-4">
                                    <div class="flex justify-between mb-2">
                                        <div class="text-sm font-medium">Incoming Leads</div>
                                        <div class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">+12 today</div>
                                    </div>
                                    <div class="h-16 bg-blue-50 rounded-lg p-2 mb-1 flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-user text-blue-500 text-sm"></i>
                                        </div>
                                        <div class="text-sm">
                                            <div class="font-medium">Sarah Johnson</div>
                                            <div class="text-xs text-gray-500">VP Marketing</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute -right-4 -top-4 bg-blue-500 text-white p-2 rounded-full text-xs font-bold">
                                    LIVE
                                </div>
                            </div>
                            <div class="countdown mb-4">00:09.4 response time</div>
                            <h3 class="text-2xl font-semibold text-center mb-3">Instant Engagement</h3>
                            <p class="text-center text-gray-600 mb-6">AI responds faster than your competition can</p>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">3. AI Speed to Lead</h2>
                    <p class="text-lg text-gray-700 mb-6">The moment a lead engages, our system responds instantly - dramatically increasing conversion rates compared to manual follow-up.</p>
                    <div class="bg-blue-50/50 p-4 rounded-xl mb-4">
                        <div class="flex justify-between items-center mb-2">
                            <div class="font-medium">Typical Manual Response:</div>
                            <div class="bg-red-100 text-red-700 px-2 py-1 rounded text-sm">47 hours</div>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="font-medium">Bella AI Response:</div>
                            <div class="bg-green-100 text-green-700 px-2 py-1 rounded text-sm">&lt; 10 seconds</div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        Leads are 21x more likely to convert when contacted within 5 minutes vs. 30 minutes.
                    </div>
                </div>
            </div>
            
            <!-- Database Reactivation -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="database-reactivation">
                <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="relative mb-6">
                                <div class="w-32 h-32 bg-gray-100 rounded-xl shadow-md p-4 text-center flex flex-col items-center justify-center reactivating">
                                    <i class="fas fa-database text-4xl text-gray-400 mb-2"></i>
                                    <span class="text-sm font-medium">Old Leads</span>
                                    <span class="text-xs text-gray-500">23 dormant accounts</span>
                                </div>
                                <div class="absolute -right-2 -top-2 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center animate-pulse">
                                    <i class="fas fa-bolt"></i>
                                </div>
                            </div>
                            <h3 class="text-2xl font-semibold text-center mb-3">Dormant Lead Revival</h3>
                            <p class="text-center text-gray-600 mb-6">Automatically re-engage cold contacts when conditions change</p>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">4. Smart Reactivation</h2>
                    <p class="text-lg text-gray-700 mb-6">Our system continuously monitors for trigger events to revive old leads. When a company raises funding, hires new executives, or shows buying signals - we re-engage.</p>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-calendar-check text-blue-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">Trigger-Based Followup</div>
                                <div class="text-sm text-gray-600">When conditions change, AI initiates outreach with relevant context</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-chart-line text-green-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">32% Conversion Rate</div>
                                <div class="text-sm text-gray-600">Reactivated leads convert at higher rates than cold prospects</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Appointment Setting -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="appointment-setting">
                <div class="w-full md:w-1/2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="relative mb-6">
                                <div class="w-48 h-48 bg-white/90 rounded-xl shadow-md p-4">
                                    <div class="text-center font-medium mb-3">Calendar Booking</div>
                                    <div class="grid grid-cols-7 gap-1 mb-2 text-xs">
                                        <div>S</div><div>M</div><div>T</div><div>W</div><div>T</div><div>F</div><div>S</div>
                                    </div>
                                    <div class="grid grid-cols-7 gap-1 text-xs">
                                        <div></div><div></div><div>1</div><div>2</div><div>3</div><div>4</div><div>5</div>
                                        <div>6</div><div class="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">7</div>
                                        <div>8</div><div>9</div><div>10</div><div>11</div><div>12</div>
                                    </div>
                                    <div class="mt-4 space-y-2">
                                        <div class="bg-blue-50 rounded-lg p-2 text-xs flex items-center">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                            <span>2:00 PM - Product Demo</span>
                                        </div>
                                        <div class="bg-green-50 rounded-lg p-2 text-xs flex items-center">
                                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                            <span>3:30 PM - Discovery Call</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="absolute -right-4 -top-4">
                                    <div class="bg-white p-2 rounded-full shadow-lg flex">
                                        <i class="fas fa-envelope text-blue-500 mx-1"></i>
                                        <i class="fas fa-sms text-green-500 mx-1"></i>
                                    </div>
                                </div>
                            </div>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">5. Automated Scheduling</h2>
                    <p class="text-lg text-gray-700 mb-6">AI handles all the back-and-forth of finding meeting times that work for both parties, with automated reminders to reduce no-shows.</p>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-magic text-blue-500 text-sm"></i>
                            </div>
                            <span>Smart timezone detection</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-sync-alt text-green-500 text-sm"></i>
                            </div>
                            <span>Rescheduling handled automatically</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-bell text-purple-500 text-sm"></i>
                            </div>
                            <span>24h & 1h reminder sequences</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Voice AI -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="voice-ai">
                <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="phone-ring mb-6">
                                <div class="w-24 h-44 bg-white rounded-3xl shadow-xl p-4 relative">
                                    <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gray-200 rounded-full"></div>
                                    <div class="flex flex-col items-center mt-6">
                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                            <i class="fas fa-headset text-blue-500 text-xl"></i>
                                        </div>
                                        <div class="text-sm font-medium mb-2 text-center">Bella AI Assistant</div>
                                        <div class="w-16 h-4 bg-gray-100 rounded-full mb-4"></div>
                                        <div class="relative w-16 h-8">
                                            <div class="absolute inset-0 bg-blue-500 rounded-full"></div>
                                            <div class="absolute inset-1 bg-white rounded-full flex items-center justify-center">
                                                <i class="fas fa-phone-alt text-blue-500"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <h3 class="text-2xl font-semibold text-center mb-3">Voice AI Receptionist</h3>
                            <p class="text-center text-gray-600 mb-6">Natural conversations that qualify leads 24/7</p>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">6. AI Voice Conversations</h2>
                    <p class="text-lg text-gray-700 mb-6">Our AI assistant answers calls with human-like conversations to qualify leads, schedule meetings, and answer common questions.</p>
                    <div class="bg-blue-50/50 p-4 rounded-xl mb-4">
                        <div class="flex items-start mb-2">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-quote-left text-blue-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">Sample Conversation:</div>
                                <div class="text-sm text-gray-700 italic">
                                    "Hi this is Sarah with Acme Co, I had some questions about your platform?"
                                </div>
                                <div class="text-sm text-gray-700 italic ml-4 mt-1">
                                    "Hello Sarah! I can help with that. Which features are you most interested in learning about?"
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                        74% of callers can't distinguish our AI from human agents.
                    </div>
                </div>
            </div>
            
            <!-- Chat Widget -->
            <div class="flex flex-col md:flex-row items-center mb-24 mobile-timeline-item" id="chat-widget">
                <div class="w-full md:w-1/2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="w-64 bg-white rounded-xl shadow-lg overflow-hidden mb-6">
                                <div class="bg-gradient-to-r from-blue-500 to-cyan-500 p-3 text-white">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mr-2">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div>Bella AI Assistant</div>
                                    </div>
                                </div>
                                <div class="p-3 h-40 overflow-y-auto">
                                    <div class="mb-2">
                                        <div class="bg-gray-100 rounded-lg p-2 text-sm inline-block">
                                            Hi there! How can I help you today?
                                        </div>
                                    </div>
                                    <div class="mb-2">
                                        <div class="bg-gray-100 rounded-lg p-2 text-sm inline-block">
                                            Here are some quick options:
                                        </div>
                                    </div>
                                    <div class="flex space-x-2 mb-2">
                                        <button class="bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full">
                                            Pricing
                                        </button>
                                        <button class="bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full">
                                            Demo
                                        </button>
                                        <button class="bg-blue-50 hover:bg-blue-100 text-blue-600 text-xs px-3 py-1 rounded-full">
                                            Case Studies
                                        </button>
                                    </div>
                                    <div class="flex justify-center">
                                        <div class="typing-indicator bg-gray-100 p-2 rounded-full">
                                            <span class="typing-dot"></span>
                                            <span class="typing-dot"></span>
                                            <span class="typing-dot"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="border-t p-2">
                                    <input type="text" placeholder="Type your question..." class="w-full px-3 py-2 rounded-lg bg-gray-50 text-sm focus:outline-none">
                                </div>
                            </div>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 md:pl-12 timeline-connector">
                    <h2 class="text-3xl font-bold mb-4">7. Conversational AI Chat</h2>
                    <p class="text-lg text-gray-700 mb-6">Website visitors get instant answers from our AI chat that understands context and qualifies leads while your team sleeps.</p>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-comments text-purple-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">Natural Language Understanding</div>
                                <div class="text-sm text-gray-600">Answers complex questions with relevant responses</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-tasks text-green-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">Lead Qualification</div>
                                <div class="text-sm text-gray-600">Captures key details and scores leads for followup</div>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-calendar-check text-yellow-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium">Meeting Scheduling</div>
                                <div class="text-sm text-gray-600">Directly books qualified leads on your calendar</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Abandoned Cart -->
            <div class="flex flex-col md:flex-row items-center mb-16 mobile-timeline-item" id="abandoned-cart">
                <div class="w-full md:w-1/2 order-1 md:order-2 mb-8 md:mb-0">
                    <div class="glass-card p-8 card-hover h-full">
                        <div class="flex flex-col items-center">
                            <div class="relative mb-6">
                                <div class="w-56 bg-white rounded-lg shadow-md p-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h4 class="font-medium">Cart Recovery Sequence</h4>
                                        <div class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">Active</div>
                                    </div>
                                    <div class="flex items-center space-x-3 mb-3">
                                        <div class="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-shopping-cart text-blue-400"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium text-sm">Your cart is waiting!</div>
                                            <div class="text-xs text-gray-500">2 items • $247 total</div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-600 mb-4">
                                        5 customers recovered today ($1,235)
                                    </div>
                                    <div class="flex">
                                        <button class="flex-1 bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 rounded-l-lg">
                                            Send Offer
                                        </button>
                                        <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-sm py-2 rounded-r-lg">
                                            View Items
                                        </button>
                                    </div>
                                </div>
                                <div class="absolute -right-4 -bottom-4 bg-white p-2 rounded-full shadow-lg">
                                    <i class="fas fa-tag text-yellow-500"></i>
                                </div>
                            </div>
                            <button class="bg-white text-blue-600 px-6 py-2 rounded-full font-medium shadow-md hover:bg-blue-50 transition-all">
                                Experience Demo
                            </button>
                        </div>
                    </div>
                </div>
                <div class="w-full md:w-1/2 order-2 md:order-1 md:pr-12">
                    <h2 class="text-3xl font-bold mb-4">8. Lost Opportunity Recovery</h2>
                    <p class="text-lg text-gray-700 mb-6">Our system detects when leads drop off and automatically follows up with tailored messages, special offers, and alternative approaches.</p>
                    <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-4 rounded-xl">
                        <div class="font-medium mb-2">Typical Recovery Workflow:</div>
                        <ol class="text-sm list-decimal list-inside space-y-1">
                            <li>1h after: Friendly reminder with benefit recap</li>
                            <li>24h after: Customer success story + social proof</li>
                            <li>48h after: Limited-time offer (if applicable)</li>
                            <li>72h after: Alternative solution or downsell</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Final CTA -->
        <div class="glass-card p-8 md:p-12 rounded-2xl mt-16 mb-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">What Else Can Bella AI Do For You?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">Complete the picture with our full suite of AI-powered business automation tools.</p>
            
            <div class="grid md:grid-cols-2 gap-8 mb-12">
                <div class="bg-white/80 p-6 rounded-xl">
                    <h3 class="text-2xl font-bold mb-3 text-blue-600">ROI Calculator</h3>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Monthly Leads</label>
                        <input type="range" min="10" max="500" value="100" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-500 mt-1">
                            <span>10</span>
                            <span>100</span>
                            <span>500</span>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">Average Deal Size</label>
                        <select class="w-full p-2 border border-gray-300 rounded-md">
                            <option>$5,000</option>
                            <option selected>$10,000</option>
                            <option>$25,000</option>
                            <option>$50,000+</option>
                        </select>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm mb-1">Estimated Monthly Revenue Impact</div>
                        <div class="text-2xl font-bold text-blue-600">$312,500</div>
                    </div>
                </div>
                
                <div class="bg-white/80 p-6 rounded-xl">
                    <h3 class="text-2xl font-bold mb-3 text-blue-600">Schedule a Demo</h3>
                    <form>
                        <div class="mb-4">
                            <input type="text" placeholder="Your Name" class="w-full p-3 border border-gray-300 rounded-lg">
                        </div>
                        <div class="mb-4">
                            <input type="email" placeholder="Email Address" class="w-full p-3 border border-gray-300 rounded-lg">
                        </div>
                        <div class="mb-4">
                            <select class="w-full p-3 border border-gray-300 rounded-lg">
                                <option>I'm interested in...</option>
                                <option>Full Platform Demo</option>
                                <option>Lead Generation Module</option>
                                <option>AI Calling Features</option>
                                <option>Chatbot Solutions</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all">
                            Book My Personalized Demo
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="flex flex-wrap justify-center gap-4">
                <button class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-medium shadow-lg transition-all">
                    Start Free Trial
                </button>
                <button class="bg-white hover:bg-gray-100 text-blue-600 px-8 py-3 rounded-full font-medium shadow-lg transition-all">
                    Talk to Sales
                </button>
            </div>
        </div>
    </div>

    <script>
        // Initialize ScrollReveal
        ScrollReveal().reveal('.mobile-timeline-item', {
            delay: 200,
            distance: '50px',
            origin: 'bottom',
            interval: 100
        });

        // GSAP animations for more complex elements
        document.addEventListener('DOMContentLoaded', () => {
            gsap.registerPlugin(ScrollTrigger);
            
            // Animate timeline connectors
            gsap.from(".timeline-connector::after", {
                scrollTrigger: {
                    trigger: ".timeline-connector",
                    start: "top bottom",
                    toggleActions: "play none none none"
                },
                scaleY: 0,
                transformOrigin: "top center",
                duration: 1.5,
                ease: "power3.out"
            });
            
            // Message bubble animations
            gsap.to(".message-bubble", {
                scrollTrigger: {
                    trigger: "#cold-outreach",
                    start: "top 80%"
                },
                stagger: 0.2,
                duration: 0.6,
                opacity: 1,
                y: 0,
                ease: "back.out(1)"
            });
            
            // Countdown animation
            let countdown = document.querySelector('.countdown');
            if (countdown) {
                let time = 9.4;
                let interval = setInterval(() => {
                    time -= 0.1;
                    if (time <= 0) {
                        clearInterval(interval);
                        countdown.textContent = "00:00.0 response time";
                    } else {
                        countdown.textContent = `00:0${Math.floor(time)}.${(time % 1).toFixed(1).slice(2)} response time`;
                    }
                }, 100);
            }
            
            // Add hover effects to all demo buttons
            document.querySelectorAll('[id^="experience-demo"]').forEach(button => {
                button.addEventListener('mouseenter', () => {
                    gsap.to(button, {
                        scale: 1.05,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
                button.addEventListener('mouseleave', () => {
                    gsap.to(button, {
                        scale: 1,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });
            });
            
            // ROI calculator interaction
            const roiSlider = document.querySelector('input[type="range"]');
            if (roiSlider) {
                roiSlider.addEventListener('input', () => {
                    const value = parseInt(roiSlider.value);
                    const dealSizeSelect = document.querySelector('select');
                    const dealSize = parseInt(dealSizeSelect.value.replace(/\D/g, '')) || 10000;
                    
                    // Simple ROI calculation - adjust as needed
                    const estimatedValue = Math.round(value * dealSize * 0.25); // Assuming 25% conversion
                    
                    document.querySelector('.text-2xl.font-bold.text-blue-600').textContent = 
                        `$${estimatedValue.toLocaleString()}`;
                });
            }
        });
    </script>
</body>
</html>