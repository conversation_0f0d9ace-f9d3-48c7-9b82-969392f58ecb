/* ===== RESPONSIVE STYLES ===== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-content h1 {
        font-size: 4rem;
    }
    
    .section-title h2 {
        font-size: 3rem;
    }
}

/* Desktop (992px to 1199px) */
@media (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
}

/* Tablet (768px to 991px) */
@media (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 20px;
    }
    
    .header-inner {
        padding: 0.75rem 0;
    }
    
    .site-navigation {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        padding: 1rem 0;
    }
    
    .site-navigation.active {
        display: block;
    }
    
    .site-navigation ul {
        flex-direction: column;
        align-items: center;
    }
    
    .site-navigation li {
        margin: 0.5rem 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .hero-section {
        padding: 100px 0 60px;
        margin-top: 60px;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.125rem;
    }
    
    .section {
        padding: 60px 0;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .testimonial-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

/* Mobile Large (576px to 767px) */
@media (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 15px;
    }
    
    .site-header {
        padding: 0 15px;
    }
    
    .hero-section {
        padding: 80px 0 50px;
        margin-top: 70px;
    }
    
    .hero-content h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .section {
        padding: 50px 0;
    }
    
    .section-title {
        margin-bottom: 2rem;
    }
    
    .section-title h2 {
        font-size: 1.75rem;
    }
    
    .section-title p {
        font-size: 1rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .service-card {
        padding: 1.5rem;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .portfolio-item img {
        height: 200px;
    }
    
    .portfolio-overlay {
        padding: 1.5rem;
        position: static;
        transform: none;
        background: rgba(0,0,0,0.8);
    }
    
    .testimonial-grid {
        margin-top: 2rem;
    }
    
    .testimonial-card {
        padding: 1.5rem;
    }
    
    .testimonial-content {
        font-size: 1rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .btn {
        padding: 10px 25px;
        font-size: 0.9rem;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
    h4 { font-size: 1.25rem; }
    h5 { font-size: 1.125rem; }
    h6 { font-size: 1rem; }
}

/* Mobile Small (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 10px;
    }
    
    .site-header {
        padding: 0 10px;
    }
    
    .header-inner {
        padding: 0.5rem 0;
    }
    
    .site-logo img {
        max-height: 40px;
    }
    
    .hero-section {
        padding: 70px 0 40px;
        margin-top: 60px;
    }
    
    .hero-content h1 {
        font-size: 1.75rem;
        line-height: 1.3;
    }
    
    .hero-content p {
        font-size: 0.95rem;
    }
    
    .section {
        padding: 40px 0;
    }
    
    .section-title h2 {
        font-size: 1.5rem;
        line-height: 1.3;
    }
    
    .section-title p {
        font-size: 0.95rem;
    }
    
    .service-card {
        padding: 1.25rem;
    }
    
    .service-icon {
        font-size: 2.5rem;
    }
    
    .portfolio-item img {
        height: 180px;
    }
    
    .portfolio-overlay {
        padding: 1rem;
    }
    
    .testimonial-card {
        padding: 1.25rem;
    }
    
    .testimonial-content {
        font-size: 0.95rem;
        margin-bottom: 1rem;
    }
    
    .author-avatar {
        width: 40px;
        height: 40px;
    }
    
    .author-info h4 {
        font-size: 0.9rem;
    }
    
    .author-info span {
        font-size: 0.8rem;
    }
    
    .site-footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-section {
        margin-bottom: 1.5rem;
    }
    
    .btn {
        padding: 8px 20px;
        font-size: 0.85rem;
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .btn + .btn {
        margin-left: 0;
    }
    
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
    h4 { font-size: 1.125rem; }
    h5 { font-size: 1rem; }
    h6 { font-size: 0.9rem; }
}

/* Extra Small Mobile (up to 375px) */
@media (max-width: 375px) {
    .hero-content h1 {
        font-size: 1.5rem;
    }
    
    .section-title h2 {
        font-size: 1.25rem;
    }
    
    .service-card,
    .testimonial-card {
        padding: 1rem;
    }
    
    .portfolio-overlay {
        padding: 0.75rem;
    }
    
    .btn {
        padding: 6px 15px;
        font-size: 0.8rem;
    }
}
