

  <!DOCTYPE html>
  <html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Introducing Our New AI-Powered Platform</title>
    <style>
      body, html {
        margin: 0;
        padding: 0;
        font-family: 'Inter', sans-serif;
        color: #24201d;
        background-color: #f5f5f5;
      }
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #FFFFFF;
        border-radius: 8px;
        overflow: hidden;
      }
      .header {
        padding: 30px 20px;
        background-color: #14100b;
        color: #FFFFFF;
        position: relative;
      }
      .header-content {
        position: relative;
        z-index: 2;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #14100b, #ff4a0250);
        z-index: 1;
      }
      .header-bg-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.2;
        background-image: url('https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1932&auto=format&fit=crop');
        background-size: cover;
        background-position: center;
        z-index: 0;
      }
      .logo {
        display: flex;
        align-items: center;
      }
      .logo-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #ff4a02;
        color: #14100b;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
        font-size: 18px;
      }
      .logo-text {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        letter-spacing: -0.025em;
        color: #FFFFFF;
      }
      .subheader {
        color: #FFFFFFCC;
        font-size: 14px;
      }
      
      .content {
        padding: 30px 20px;
        background-color: #FFFFFF;
      }
      .divider {
        text-align: center;
        margin-bottom: 20px;
        position: relative;
      }
      .divider:before, .divider:after {
        content: "";
        display: inline-block;
        width: 30%;
        height: 1px;
        background: linear-gradient(to right, transparent, #ff4a0280, transparent);
        vertical-align: middle;
      }
      .divider-text {
        display: inline-block;
        padding: 0 10px;
        text-transform: uppercase;
        font-size: 14px;
        color: #24201dB3;
      }
      h1 {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 24px;
        margin-bottom: 20px;
        text-align: center;
      }
      p {
        line-height: 1.6;
        margin-bottom: 16px;
      }
      .greeting {
        margin-bottom: 20px;
      }
      .name-highlight {
        font-weight: 600;
        color: #14100b;
      }
      .date-highlight {
        font-weight: 500;
        color: #14100b;
        position: relative;
        display: inline-block;
      }
      .date-highlight:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30%;
        background-color: #ff4a0233;
        z-index: -1;
        border-radius: 2px;
      }
      .benefits-box {
        background-color: #f9f9f9;
        border: 1px solid #ff4a0220;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
      }
      .benefits-title {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: 500;
      }
      .benefit-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 10px;
      }
      .benefit-icon {
        color: #ff4a02;
        margin-right: 10px;
        flex-shrink: 0;
      }
      .benefit-text {
        font-size: 14px;
      }
      .investment-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 30px;
      }
      .investment-card {
        flex: 1 1 calc(50% - 8px);
        min-width: 200px;
        border: 1px solid #ff4a0220;
        border-radius: 8px;
        padding: 16px;
        position: relative;
        background-color: #FFFFFF;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
      .card-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
      }
      .card-title {
        font-weight: 600;
        color: #14100b;
        margin-bottom: 4px;
      }
      .card-subtitle {
        font-size: 12px;
        color: #24201d99;
      }
      .card-percentage {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: #ff4a0215;
        border: 1px solid #ff4a0240;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .percentage-value {
        font-weight: bold;
        color: #14100b;
        font-size: 16px;
      }
      .percentage-symbol {
        font-size: 10px;
        color: #14100b99;
        margin-top: -2px;
      }
      .card-description {
        font-size: 14px;
      }
      .card-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 4px;
        width: 100%;
        background: linear-gradient(to right, #14100b80, #14100b30);
      }
      .featured-section {
        margin-bottom: 30px;
      }
      .featured-title {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      .featured-icon {
        color: #ff4a02;
        margin-right: 8px;
      }
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
      }
      .feature-icon-circle {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #f9f9f9;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
      }
      .feature-icon {
        color: #14100b;
      }
      .cta-container {
        text-align: center;
        margin-bottom: 30px;
      }
      .cta-button {
        display: inline-block;
        padding: 12px 24px;
        background-color: #ff4a02;
        color: white;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        margin-bottom: 16px;
      }
      .cta-note {
        font-size: 12px;
        color: #24201dB3;
      }
      .resources-title {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 18px;
        margin-bottom: 15px;
        font-weight: 500;
        text-align: center;
      }
      .resources-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 30px;
      }
      .resource-card {
        flex: 1 1 calc(33.333% - 11px);
        min-width: 150px;
        border: 1px solid #ff4a0220;
        border-radius: 8px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        text-decoration: none;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        background-color: #FFFFFF;
      }
      .resource-icon-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background-color: #14100b0D;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
      }
      .resource-icon {
        color: #14100b;
      }
      .resource-title {
        font-weight: 500;
        color: #14100b;
        margin-bottom: 4px;
      }
      .resource-description {
        font-size: 12px;
        color: #24201dB3;
      }
      .footer {
        background-color: #14100b;
        color: #FFFFFF;
        padding: 20px;
      }
      .footer-content {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 20px;
      }
      .footer-logo {
        margin-bottom: 16px;
      }
      .footer-links {
        display: flex;
        gap: 16px;
      }
      .footer-link {
        color: #FFFFFFCC;
        text-decoration: none;
        font-size: 14px;
      }
      .footer-bottom {
        text-align: center;
        padding-top: 16px;
        border-top: 1px solid #FFFFFF1A;
      }
      .copyright {
        font-size: 12px;
        color: #FFFFFF99;
      }
      .methodology {
        margin-bottom: 30px;
      }
      .methodology-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        border-top: 1px solid #ff4a022060;
      }
      .step-number {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #ff4a0215;
        border: 1px solid #ff4a0230;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        color: #ff4a02;
        font-size: 12px;
        font-weight: 600;
      }
      
      /* Why Join Section */
      .why-join-section {
        background-color: #f9f9f960;
        border: 1px solid #ff4a0220;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 30px;
      }
      
      .why-join-title {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 20px;
        margin-bottom: 16px;
        font-weight: 600;
      }
      
      .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 14px;
      }
      
      .benefit-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #ff4a02;
        margin-right: 8px;
      }
      
      /* Your Role Section */
      .role-section {
        margin-bottom: 30px;
      }
      
      .role-title {
        font-family: 'SF Pro Display', 'Inter', sans-serif;
        color: #14100b;
        font-size: 20px;
        margin-bottom: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
      }
      
      .role-title-icon {
        color: #ff4a02;
        margin-right: 8px;
      }
      
      .role-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
      }
      
      .role-icon {
        width: 28px;
        height: 28px;
        margin-right: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      @media only screen and (max-width: 480px) {
        .header-content {
          flex-direction: column;
          align-items: flex-start;
        }
        .subheader {
          margin-top: 8px;
        }
        .investment-card, .resource-card {
          flex: 1 1 100%;
        }
        .feature-icon-circle {
          width: 24px;
          height: 24px;
        }
        .benefits-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <!-- Header -->
      <div class="header">
        <div class="header-bg"></div>
        
        <div class="header-content">
          <div class="logo">
            <img src="https://storage.googleapis.com/lovable-projects/lovable-uploads/bc650133-e099-4ebb-9b93-65bbf576e820.png" alt="AxonFlash Logo" width="32" height="32" style="margin-right: 10px;">
            <div class="logo-text">Tech Solutions</div>
          </div>
          <div class="subheader">AI Innovation</div>
        </div>
      </div>
      
      <!-- Content -->
      <div class="content">
        <div class="divider">
          <span class="divider-text">New Release</span>
        </div>
        
        <h1>Revolutionizing Automation with AI</h1>
        
        
          <p class="greeting">
            Dear <span class="name-highlight">Valued Client</span>,
          </p>
        
        
        
          <p>Dear Valued Client,
          </p>
        
          <p>We are thrilled to announce the launch of our new AI-powered platform, designed to revolutionize your automation processes. This platform leverages cutting-edge artificial intelligence to provide unparalleled efficiency and insights.
          </p>
        
          <p>With our new platform, you can automate complex tasks, optimize workflows, and gain a competitive edge in today's fast-paced market.
          </p>
        
        
        
        
        
        
        
          <div class="benefits-box">
            <h3 class="benefits-title">Key Features</h3>
            
            <div class="benefits-list">
              
                <div class="benefit-item">
                  <div class="benefit-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#ff4a02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></div>
                  <div class="benefit-text">AI-powered automation</div>
                </div>
              
                <div class="benefit-item">
                  <div class="benefit-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#ff4a02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></div>
                  <div class="benefit-text">Real-time data analytics</div>
                </div>
              
                <div class="benefit-item">
                  <div class="benefit-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#ff4a02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></div>
                  <div class="benefit-text">Customizable workflows</div>
                </div>
              
                <div class="benefit-item">
                  <div class="benefit-icon"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#ff4a02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg></div>
                  <div class="benefit-text">Seamless integration with existing systems</div>
                </div>
              
            </div>
          </div>
        
        
        
        
        
        
        
        
        
        
        <div class="cta-container">
          <a href="#action" class="cta-button">
            Learn More <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
          </a>
          
          <p class="cta-note">
            Thank you for your continued partnership
          </p>
        </div>
        
        
        
        <h3 class="resources-title">Additional Resources</h3>
        
        <div class="resources-grid">
          <a href="#pdf" class="resource-card">
            <div class="resource-icon-circle">
              <div class="resource-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#14100b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg></div>
            </div>
            <div class="resource-title">Product Overview</div>
            <div class="resource-description">Key features and benefits</div>
          </a>
          
          <a href="#ppt" class="resource-card">
            <div class="resource-icon-circle">
              <div class="resource-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#14100b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"></path><path d="M18.4 9l-6-6-7 7"></path><path d="M18.4 15l-6-6-7 7"></path></svg></div>
            </div>
            <div class="resource-title">Success Stories</div>
            <div class="resource-description">Case studies and results</div>
          </a>
          
          <a href="#website" class="resource-card">
            <div class="resource-icon-circle">
              <div class="resource-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#14100b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="2" y1="12" x2="22" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg></div>
            </div>
            <div class="resource-title">Knowledge Base</div>
            <div class="resource-description">Interactive resources</div>
          </a>
        </div>
      </div>
      
      <!-- Footer -->
      <div class="footer">
        <div class="footer-content">
          <div class="footer-logo">
            <div style="font-family: 'SF Pro Display', 'Inter', sans-serif; color: white;">
              Tech Solutions
            </div>
            <div style="color: #ff4a02; font-size: 14px;">
              AI Innovation
            </div>
          </div>
          
          <div class="footer-links">
            <a href="#contact" class="footer-link">Contact</a>
            <a href="#privacy" class="footer-link">Privacy</a>
            <a href="#terms" class="footer-link">Terms</a>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p class="copyright">
            © 2025 Tech Solutions. All rights reserved.<br>
            This email was sent to you because you have an active subscription with us.
          </p>
        </div>
      </div>
    </div>
  </body>
  </html>
  