<!DOCTYPE html>
<html lang="en-US" class="no-js no-svg">

<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon"
        href="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/images/favicon.png"
        type="image/x-icon">
    <link rel="icon" href="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/images/favicon.png"
        type="image/x-icon">
    <!-- responsive meta -->
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- For IE -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Braine</title>
    <meta name='robots' content='noindex, nofollow' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel='dns-prefetch' href='//fonts.googleapis.com' />
    <link rel="alternate" type="application/rss+xml" title="Braine &raquo; Feed"
        href="https://data.themeim.com/wp/braine/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Braine &raquo; Comments Feed"
        href="https://data.themeim.com/wp/braine/comments/feed/" />
    <script type="text/javascript">
        /* <![CDATA[ */
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/data.themeim.com\/wp\/braine\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
        /* ]]> */
    </script>
    <style id='wp-emoji-styles-inline-css' type='text/css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='classic-theme-styles-inline-css' type='text/css'>
        /*! This file is auto-generated */
        .wp-block-button__link {
            color: #fff;
            background-color: #32373c;
            border-radius: 9999px;
            box-shadow: none;
            text-decoration: none;
            padding: calc(.667em + 2px) calc(1.333em + 2px);
            font-size: 1.125em
        }

        .wp-block-file__button {
            background: #32373c;
            color: #fff;
            text-decoration: none
        }
    </style>
    <style id='global-styles-inline-css' type='text/css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--color--strong-yellow: #f7bd00;
            --wp--preset--color--strong-white: #fff;
            --wp--preset--color--light-black: #242424;
            --wp--preset--color--very-light-gray: #797979;
            --wp--preset--color--very-dark-black: #000000;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 10px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 24px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--font-size--normal: 15px;
            --wp--preset--font-size--huge: 36px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :where(.is-layout-flex) {
            gap: 0.5em;
        }

        :where(.is-layout-grid) {
            gap: 0.5em;
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :where(.wp-block-post-template.is-layout-flex) {
            gap: 1.25em;
        }

        :where(.wp-block-post-template.is-layout-grid) {
            gap: 1.25em;
        }

        :where(.wp-block-columns.is-layout-flex) {
            gap: 2em;
        }

        :where(.wp-block-columns.is-layout-grid) {
            gap: 2em;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
    </style>


    <link rel='stylesheet' id='bootstrap-css'
        href='https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/css/bootstrap.css?ver=6.8.1'
        type='text/css' media='all' />
    <link rel='stylesheet' id='global-css'
        href='https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/css/global.css?ver=1748680522'
        type='text/css' media='all' />
 








    <link rel='stylesheet' id='braine-main-braine-style-css'
        href='https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/css/braine-style.css?ver=1748680522'
        type='text/css' media='all' />

    <link rel='stylesheet' id='braine-responsive-css'
        href='https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/css/responsive.css?ver=1748680522'
        type='text/css' media='all' />


    <link rel='stylesheet' id='elementor-post-8-css'
        href='https://data.themeim.com/wp/braine/wp-content/uploads/elementor/css/post-8.css?ver=1746508539'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-post-9-css'
        href='https://data.themeim.com/wp/braine/wp-content/uploads/elementor/css/post-9.css?ver=1746508539'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-roboto-css'
        href='https://data.themeim.com/wp/braine/wp-content/uploads/elementor/google-fonts/css/roboto.css?ver=1744973288'
        type='text/css' media='all' />
    <link rel='stylesheet' id='elementor-gf-local-robotoslab-css'
        href='https://data.themeim.com/wp/braine/wp-content/uploads/elementor/google-fonts/css/robotoslab.css?ver=1744973290'
        type='text/css' media='all' />
    <script type="text/javascript" id="jquery-core-js-extra">
        /* <![CDATA[ */
        var braine_data = { "ajaxurl": "https:\/\/data.themeim.com\/wp\/braine\/wp-admin\/admin-ajax.php", "nonce": "e3ef58193c" };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <link rel="https://api.w.org/" href="https://data.themeim.com/wp/braine/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://data.themeim.com/wp/braine/wp-json/wp/v2/pages/9" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD"
        href="https://data.themeim.com/wp/braine/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="https://data.themeim.com/wp/braine/" />
    <link rel='shortlink' href='https://data.themeim.com/wp/braine/' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://data.themeim.com/wp/braine/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fdata.themeim.com%2Fwp%2Fbraine%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://data.themeim.com/wp/braine/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fdata.themeim.com%2Fwp%2Fbraine%2F&#038;format=xml" />
    <meta name="generator" content="Redux *******" />
    <meta name="generator"
        content="Elementor 3.28.4; features: e_font_icon_svg, additional_custom_breakpoints, e_local_google_fonts; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <!-- Universal Chat Widget -->
    <script src="universal-chat-widget.js"></script>
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
</head>


<body
    class="home wp-singular page-template page-template-tpl-default-elementor page-template-tpl-default-elementor-php page page-id-9 wp-theme-braine menu-layer elementor-default elementor-kit-8 elementor-page elementor-page-9">


    <div class="page-wrapper">
        <div data-elementor-type="wp-page" data-elementor-id="9" class="elementor elementor-9">
           
          
           
           
           
            
            <div class="elementor-element elementor-element-c51a2eb e-con-full e-flex e-con e-parent" data-id="c51a2eb"
                data-element_type="container">
                <div class="elementor-element elementor-element-75e47f6 elementor-widget elementor-widget-braine_instagram_section"
                    data-id="75e47f6" data-element_type="widget" data-widget_type="braine_instagram_section.default">
                    <div class="elementor-widget-container">


                        <!-- Social One -->
                        <section class="social-one te-icon-box">
                            <div class="social-one_bg-shadow"
                                style="background-image:url(https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-bg.png)">
                            </div>
                            <div class="auto-container">
                                <!-- Sec Title -->
                                <div class="sec-title centered">
                                    <div class="sec-title_title">Integrations</div>
                                    <h2 class="sec-title_heading">Incorporate our tool into <br> <span>your everyday
                                            tasks</span></h2>
                                </div>
                                <div class="social-one_logo">
                                    <span><img decoding="async"
                                            src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-logo.png"
                                            alt="Awesome Image" /></span>
                                </div>
                                <div class="inner-container">
                                    <div class="social-one_bg"
                                        style="background-image:url(https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-one_pattern.png)">
                                    </div>
                                    <div class="social-one_bg-two"
                                        style="background-image:url(https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-one_pattern-two.png)">
                                    </div>

                                    <div class="social-box_one">
                                        <div class="animation_mode">
                                          
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-1.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                        
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-2.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-3.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-4.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-5.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-6.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-7.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="social-box">
                                        <div class="animation_mode_two">
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-8.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-9.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-10.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-11.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-12.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-13.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                            <!-- Icon -->
                                            <div class="social_icon-box">
                                                <a href="#">
                                                    <img decoding="async"
                                                        src="https://data.themeim.com/wp/braine/wp-content/uploads/2024/03/social-14.png"
                                                        alt="Awesome Image" /> </a>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </section>
                        <!-- End Social One -->


                    </div>
                </div>
            </div>
           
        </div>



       



    </div>



    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/wp\/braine\/*"},{"not":{"href_matches":["\/wp\/braine\/wp-*.php","\/wp\/braine\/wp-admin\/*","\/wp\/braine\/wp-content\/uploads\/*","\/wp\/braine\/wp-content\/*","\/wp\/braine\/wp-content\/plugins\/*","\/wp\/braine\/wp-content\/themes\/braine\/*","\/wp\/braine\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>(function () {
            function maybePrefixUrlField() {
                const value = this.value.trim()
                if (value !== '' && value.indexOf('http') !== 0) {
                    this.value = 'http://' + value
                }
            }

            const urlFields = document.querySelectorAll('.mc4wp-form input[type="url"]')
            for (let j = 0; j < urlFields.length; j++) {
                urlFields[j].addEventListener('blur', maybePrefixUrlField)
            }
        })();</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
        id="wp-hooks-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
        id="wp-i18n-js"></script>
    <script type="text/javascript" id="wp-i18n-js-after">
        /* <![CDATA[ */
        wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/contact-form-7/includes/swv/js/index.js?ver=6.0.6"
        id="swv-js"></script>
    <script type="text/javascript" id="contact-form-7-js-before">
        /* <![CDATA[ */
        var wpcf7 = {
            "api": {
                "root": "https:\/\/data.themeim.com\/wp\/braine\/wp-json\/",
                "namespace": "contact-form-7\/v1"
            }
        };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/contact-form-7/includes/js/index.js?ver=6.0.6"
        id="contact-form-7-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/popper.min.js?ver=2.1.2"
        id="popper-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/bootstrap.min.js?ver=2.1.2"
        id="bootstrap-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/appear.js?ver=2.1.2"
        id="appear-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/parallax.min.js?ver=2.1.2"
        id="parallax-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/tilt.jquery.min.js?ver=2.1.2"
        id="tilt-jquery-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/jquery.paroller.min.js?ver=2.1.2"
        id="jquery-paroller-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/wow.js?ver=2.1.2"
        id="wow-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/elementor/assets/lib/swiper/v8/swiper.min.js?ver=8.4.5"
        id="swiper-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/backtotop.js?ver=2.1.2"
        id="backtotop-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/odometer.js?ver=2.1.2"
        id="odometer-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/parallax-scroll.js?ver=2.1.2"
        id="parallax-scroll-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/gsap.min.js?ver=2.1.2"
        id="gsap-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/SplitText.min.js?ver=2.1.2"
        id="splittext-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/ScrollTrigger.min.js?ver=2.1.2"
        id="scrolltrigger-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/ScrollToPlugin.min.js?ver=2.1.2"
        id="scrolltoplugin-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/ScrollSmoother.min.js?ver=2.1.2"
        id="scrollsmoother-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/touchspin.js?ver=2.1.2"
        id="touchspin-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/jquery.marquee.min.js?ver=2.1.2"
        id="jquery-marquee-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/magnific-popup.min.js?ver=2.1.2"
        id="magnific-popup-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/nav-tool.js?ver=2.1.2"
        id="nav-tool-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/jquery-ui.js?ver=2.1.2"
        id="jquery-ui-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/element-in-view.js?ver=2.1.2"
        id="element-in-view-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/themes/braine/assets/js/main-script.js?ver=1748680522"
        id="braine-main-script-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-includes/js/comment-reply.min.js?ver=6.8.1" id="comment-reply-js"
        async="async" data-wp-strategy="async"></script>
    <script type="text/javascript" defer
        src="https://data.themeim.com/wp/braine/wp-content/plugins/mailchimp-for-wp/assets/js/forms.js?ver=4.10.3"
        id="mc4wp-forms-api-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.28.4"
        id="elementor-webpack-runtime-js"></script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.28.4"
        id="elementor-frontend-modules-js"></script>
    <script type="text/javascript" id="elementor-frontend-js-before">
        /* <![CDATA[ */
        var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": false }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": false }, "version": "3.28.4", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_local_google_fonts": true, "nested-elements": true, "editor_v2": true, "home_screen": true }, "urls": { "assets": "https:\/\/data.themeim.com\/wp\/braine\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/data.themeim.com\/wp\/braine\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/data.themeim.com\/wp\/braine\/wp-content\/uploads" }, "nonces": { "floatingButtonsClickTracking": "f5f3f88988" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "active_breakpoints": ["viewport_mobile", "viewport_tablet"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description" }, "post": { "id": 9, "title": "Braine", "excerpt": "", "featuredImage": false } };
        /* ]]> */
    </script>
    <script type="text/javascript"
        src="https://data.themeim.com/wp/braine/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.28.4"
        id="elementor-frontend-js"></script>
</body>

</html>